package com.org.panaroma.commons.dto.mandate;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.avro.reflect.Nullable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class ActionMetadata implements Serializable {

	@Nullable
	@JsonProperty("executionNo")
	Integer executionNo;

	@Nullable
	@JsonProperty("retryAttempt")
	Integer retryAttempt;

	@Nullable
	@JsonProperty("pauseEndDate")
	Long pauseEndDate;

	@Nullable
	@JsonProperty("expiryDate")
	Long expiryDate;

	@Nullable
	@JsonProperty("validityEndDate")
	Long validityEndDate;

	@Nullable
	@JsonProperty("isBackFilled")
	Boolean isBackFilled;

	@Override
	public String toString() {
		return "ActionMetadata{" + "executionNo=" + executionNo + ", retryAttempt=" + retryAttempt + ", pauseEndDate="
				+ pauseEndDate + ", validityEndDate=" + validityEndDate + ", isBackFilled=" + isBackFilled + '}';
	}

}
